name: Deploy Leptos Book to Github Pages

on:
  # Runs on pushes targeting the default branch
  push:
    branches: ["main"]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# Sets permissions of the GITHUB_TOKEN to allow deployment to GitHub Pages
permissions:
  contents: read
  pages: write
  id-token: write

# Allow only one concurrent deployment, skipping runs queued between the run in-progress and latest queued.
# However, do NOT cancel in-progress runs as we want to allow these production deployments to complete.
concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  # Job: Build and Deploy to Pages
  build-and-deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}

    runs-on: ubuntu-latest
    env:
      MDBOOK_VERSION: 0.4.36
    steps:
      - uses: actions/checkout@v4
      - name: Install mdBook
        run: |
          curl --proto '=https' --tlsv1.3 https://sh.rustup.rs -sSf -y | sh
          rustup update
          cargo install --version ${MDBOOK_VERSION} mdbook

      - name: Install mdbook-admonish
        run: |
          cargo install mdbook-admonish --vers "1.15.0" --locked
          mdbook-admonish install ./

      - name: Setup Pages
        id: pages
        uses: actions/configure-pages@v4

      - name: Build with mdBook
        run: mdbook build

      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: ./book

      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
