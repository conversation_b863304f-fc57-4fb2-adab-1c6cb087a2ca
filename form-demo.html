<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Form Component Demo - Router Chapter</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
        }

        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }

        h2 {
            color: #34495e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
            margin-top: 30px;
        }

        .url-display {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .url-display h3 {
            margin-top: 0;
            color: #2c3e50;
        }

        .param-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .param-table th,
        .param-table td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
        }

        .param-table th {
            background-color: #f1f3f4;
            font-weight: 600;
        }

        .param-table code {
            background: #e8f4f8;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Monaco', 'Menlo', monospace;
        }

        .form-section {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }

        input[type="text"],
        input[type="number"],
        select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        input[type="text"]:focus,
        input[type="number"]:focus,
        select:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        input[type="submit"] {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s ease;
        }

        input[type="submit"]:hover {
            background: #2980b9;
        }

        .current-url {
            background: #e8f4f8;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            word-break: break-all;
            margin-top: 10px;
        }

        .auto-submit-note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 15px;
            font-size: 14px;
            color: #856404;
        }
    </style>
</head>
<body>
    <h1>🚀 Form Component Demo</h1>
    <p style="text-align: center; color: #666;">
        复刻Router章节中Form组件的功能：借助form元素往URL中加query参数
    </p>

    <!-- URL参数显示区域 -->
    <div class="url-display">
        <h3>📊 当前URL参数</h3>
        <table class="param-table">
            <thead>
                <tr>
                    <th>参数名</th>
                    <th>参数值</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><code>name</code></td>
                    <td id="display-name">-</td>
                </tr>
                <tr>
                    <td><code>number</code></td>
                    <td id="display-number">-</td>
                </tr>
                <tr>
                    <td><code>select</code></td>
                    <td id="display-select">-</td>
                </tr>
            </tbody>
        </table>
        <div class="current-url">
            <strong>完整URL:</strong> <span id="current-url"></span>
        </div>
    </div>

    <!-- 手动提交表单 -->
    <div class="form-section">
        <h2>📝 手动提交表单</h2>
        <p>填写表单后点击提交按钮，URL会更新为包含表单数据的query参数</p>
        
        <form id="manual-form" method="GET">
            <div class="form-group">
                <label for="manual-name">姓名:</label>
                <input type="text" id="manual-name" name="name" placeholder="请输入姓名">
            </div>
            
            <div class="form-group">
                <label for="manual-number">数字:</label>
                <input type="number" id="manual-number" name="number" placeholder="请输入数字">
            </div>
            
            <div class="form-group">
                <label for="manual-select">选择:</label>
                <select id="manual-select" name="select">
                    <option value="">请选择...</option>
                    <option value="A">选项 A</option>
                    <option value="B">选项 B</option>
                    <option value="C">选项 C</option>
                </select>
            </div>
            
            <input type="submit" value="🚀 提交表单">
        </form>
    </div>

    <!-- 自动提交表单 -->
    <div class="form-section">
        <h2>⚡ 自动提交表单</h2>
        <div class="auto-submit-note">
            💡 <strong>自动提交:</strong> 输入内容时会自动提交表单，实时更新URL参数
        </div>
        
        <form id="auto-form" method="GET">
            <div class="form-group">
                <label for="auto-name">姓名 (自动提交):</label>
                <input type="text" id="auto-name" name="name" placeholder="输入时自动提交">
            </div>
            
            <div class="form-group">
                <label for="auto-number">数字 (自动提交):</label>
                <input type="number" id="auto-number" name="number" placeholder="输入时自动提交">
            </div>
            
            <div class="form-group">
                <label for="auto-select">选择 (自动提交):</label>
                <select id="auto-select" name="select">
                    <option value="">请选择...</option>
                    <option value="A">选项 A</option>
                    <option value="B">选项 B</option>
                    <option value="C">选项 C</option>
                </select>
            </div>
        </form>
    </div>

    <script>
        // 获取URL参数的工具函数
        function getUrlParams() {
            const urlParams = new URLSearchParams(window.location.search);
            return {
                name: urlParams.get('name') || '',
                number: urlParams.get('number') || '',
                select: urlParams.get('select') || ''
            };
        }

        // 更新显示区域
        function updateDisplay() {
            const params = getUrlParams();
            
            document.getElementById('display-name').textContent = params.name || '-';
            document.getElementById('display-number').textContent = params.number || '-';
            document.getElementById('display-select').textContent = params.select || '-';
            document.getElementById('current-url').textContent = window.location.href;
            
            // 同步表单值
            document.getElementById('manual-name').value = params.name;
            document.getElementById('manual-number').value = params.number;
            document.getElementById('manual-select').value = params.select;
            
            document.getElementById('auto-name').value = params.name;
            document.getElementById('auto-number').value = params.number;
            document.getElementById('auto-select').value = params.select;
        }

        // 提交表单并更新URL
        function submitForm(formData) {
            const params = new URLSearchParams();
            
            // 只添加非空值到URL参数中
            for (const [key, value] of formData.entries()) {
                if (value.trim() !== '') {
                    params.set(key, value);
                }
            }
            
            // 更新URL但不刷新页面
            const newUrl = window.location.pathname + (params.toString() ? '?' + params.toString() : '');
            window.history.pushState({}, '', newUrl);
            
            // 更新显示
            updateDisplay();
        }

        // 手动提交表单事件处理
        document.getElementById('manual-form').addEventListener('submit', function(e) {
            e.preventDefault(); // 阻止默认的表单提交行为
            const formData = new FormData(this);
            submitForm(formData);
        });

        // 自动提交表单事件处理
        function setupAutoSubmit() {
            const autoForm = document.getElementById('auto-form');
            const inputs = autoForm.querySelectorAll('input, select');
            
            inputs.forEach(input => {
                const eventType = input.type === 'select-one' ? 'change' : 'input';
                input.addEventListener(eventType, function() {
                    // 模拟 this.form.requestSubmit() 的行为
                    const formData = new FormData(autoForm);
                    submitForm(formData);
                });
            });
        }

        // 监听浏览器前进后退按钮
        window.addEventListener('popstate', updateDisplay);

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateDisplay();
            setupAutoSubmit();
        });
    </script>
</body>
</html>
