# Summary

- [Introduction](./01_introduction.md)
- [Getting Started](./getting_started/README.md)
  - [Leptos DX](./getting_started/leptos_dx.md)
  - [The Leptos Community and leptos-* Crates](./getting_started/community_crates.md)
- [Part 1: Building User Interfaces](./view/README.md)
  - [A Basic Component](./view/01_basic_component.md)
  - [Dynamic Attributes](./view/02_dynamic_attributes.md)
  - [Components and Props](./view/03_components.md)
  - [Iteration](./view/04_iteration.md)
  - [Iterating over More Complex Data](./view/04b_iteration.md)
  - [Forms and Inputs](./view/05_forms.md)
  - [Control Flow](./view/06_control_flow.md)
  - [Error Handling](./view/07_errors.md)
  - [Parent-Child Communication](./view/08_parent_child.md)
  - [Passing Children to Components](./view/09_component_children.md)
  - [No Macros: The View Builder Syntax](./view/builder.md)
- [Reactivity](./reactivity/README.md)
  - [Working with Signals](./reactivity/working_with_signals.md)
  - [Responding to Changes with Effects](./reactivity/14_create_effect.md)
  - [Interlude: Reactivity and Functions](./reactivity/interlude_functions.md)
- [Testing](./testing.md)
- [Async](./async/README.md)
  - [Loading Data with Resources](./async/10_resources.md)
  - [Suspense](./async/11_suspense.md)
  - [Transition](./async/12_transition.md)
  - [Actions](./async/13_actions.md)
- [Interlude: Projecting Children](./interlude_projecting_children.md)
- [Global State Management](./15_global_state.md)
- [Router](./router/README.md)
  - [Defining `<Routes/>`](./router/16_routes.md)
  - [Nested Routing](./router/17_nested_routing.md)
  - [Params and Queries](./router/18_params_and_queries.md)
  - [`<A/>`](./router/19_a.md)
  - [`<Form/>`](./router/20_form.md)
- [Interlude: Styling](./interlude_styling.md)
- [Metadata](./metadata.md)
- [Integrating with JavaScript: `wasm-bindgen`, `web_sys`, and `HtmlElement`](./web_sys.md)
- [Client-Side Rendering: Wrapping Up](./csr_wrapping_up.md)
- [Part 2: Server Side Rendering](./ssr/README.md)
  - [`cargo-leptos`](./ssr/21_cargo_leptos.md)
  - [The Life of a Page Load](./ssr/22_life_cycle.md)
  - [Async Rendering and SSR “Modes”](./ssr/23_ssr_modes.md)
  - [Hydration Bugs](./ssr/24_hydration_bugs.md)
- [Working with the Server](./server/README.md)
  - [Server Functions](./server/25_server_functions.md)
  - [Extractors](./server/26_extractors.md)
  - [Responses and Redirects](./server/27_response.md)
- [Progressive Enhancement and Graceful Degradation](./progressive_enhancement/README.md)
  - [`<ActionForm/>`](./progressive_enhancement/action_form.md)
- [Deployment](./deployment/README.md)
  - [Deploying CSR Apps](./deployment/csr.md)
  - [Deploying SSR Apps](./deployment/ssr.md)
  - [Optimizing WASM Binary Size](./deployment/binary_size.md)
- [Guide: Islands](./islands.md)

- [Appendix: How Does the Reactive System Work?](./appendix_reactive_graph.md)
- [Appendix: The Life Cycle of a Signal](./appendix_life_cycle.md)

