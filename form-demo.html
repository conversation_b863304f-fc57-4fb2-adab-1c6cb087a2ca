<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Form Demo</title>
    <style>
        body {
            font-family: system-ui, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }

        .section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }

        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }

        button {
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .url-display {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <h1>Form Demo</h1>

    <div class="url-display">
        当前URL: <span id="current-url"></span>
    </div>

    <div class="section">
        <h2>手动提交</h2>
        <form method="GET" action="" onsubmit="submitForm(event)">
            <input type="text" name="q" placeholder="搜索内容">
            <button type="submit">提交</button>
        </form>
    </div>

    <div class="section">
        <h2>自动提交</h2>
        <form method="GET" action="" onsubmit="submitForm(event)">
            <input type="text" name="q" placeholder="输入时自动提交"
                   oninput="this.form.requestSubmit()">
        </form>
    </div>

    <script>
        // 更新URL显示
        function updateDisplay() {
            document.getElementById('current-url').textContent = window.location.href;
        }

        function submitForm(e) {
            e.preventDefault();
            const formData = new FormData(e.target);
            const params = new URLSearchParams(formData);
            const newUrl = window.location.pathname + '?' + params.toString();
            window.history.pushState({}, '', newUrl);
            updateDisplay();
        }

        // 初始化
        updateDisplay();
        window.addEventListener('popstate', updateDisplay);
    </script>
</body>
</html>
